import { useState, useEffect } from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { invoiceFormSchema, type InvoiceFormData } from "@/lib/validations"
import { createInvoice, updateInvoice } from "@/lib/api/invoices-client"
import { getCurrentUserIdOrFallback } from "@/lib/auth"
import { Invoice } from "@/lib/types"
import { type Currency } from "@/lib/utils/currency"

interface UseInvoiceFormProps {
  invoice?: Invoice
  preselectedClientId?: string
  preselectedProjectId?: string
  onSuccess: () => void
  onOpenChange: (open: boolean) => void
  open: boolean
}

export function useInvoiceForm({
  invoice,
  preselectedClientId,
  preselectedProjectId,
  onSuccess,
  onOpenChange,
  open,
}: UseInvoiceFormProps) {
  const [loading, setLoading] = useState(false)
  const isEditing = !!invoice

  const form = useForm<InvoiceFormData>({
    resolver: zodResolver(invoiceFormSchema),
    defaultValues: {
      client_id: "",
      project_id: "none",
      amount: 0,
      tax_amount: 0,
      discount_amount: 0,
      discount_type: "amount" as const,
      total_amount: 0,
      currency: "IDR" as Currency,
      status: "draft",
      due_date: "",
      paid_date: "",
      items: [{ id: "1", description: "", quantity: 1, rate: 0, amount: 0 }],
      notes: "",
      milestone_type: "standard" as const,
      parent_invoice_id: undefined,
      milestone_percentage: undefined,
      sequence_number: 1,
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  })

  // Reset form when invoice data changes
  useEffect(() => {
    if (open) {
      // Handle invoice items - they might be stored as JSON in the database
      let defaultItems = [{ id: "1", description: "", quantity: 1, rate: 0, amount: 0 }]

      if (invoice?.items) {
        if (Array.isArray(invoice.items)) {
          defaultItems = invoice.items as { id: string; description: string; quantity: number; rate: number; amount: number; }[]
        } else if (typeof invoice.items === 'string') {
          try {
            defaultItems = JSON.parse(invoice.items)
          } catch (e) {
            console.error('Failed to parse invoice items:', e)
          }
        }
      }
      
      form.reset({
        client_id: invoice?.client_id || preselectedClientId || "",
        project_id: invoice?.project_id || preselectedProjectId || "none",
        amount: invoice?.amount || 0,
        tax_amount: invoice?.tax_amount || 0,
        discount_amount: invoice?.discount_amount || 0,
        discount_type: (invoice?.discount_type as "amount" | "percentage") || "amount",
        total_amount: invoice?.total_amount || 0,
        currency: (invoice?.currency as Currency) || "IDR",
        status: invoice?.status || "draft",
        due_date: invoice?.due_date || "",
        paid_date: invoice?.paid_date || "",
        items: defaultItems,
        notes: invoice?.notes || "",
        milestone_type: invoice?.milestone_type || "standard",
        parent_invoice_id: invoice?.parent_invoice_id,
        milestone_percentage: invoice?.milestone_percentage,
        sequence_number: invoice?.sequence_number || 1,
      })
    }
  }, [invoice, preselectedClientId, preselectedProjectId, open, form])

  const onSubmit = async (data: InvoiceFormData) => {
    setLoading(true)
    try {
      // Prepare base invoice data
      const baseInvoiceData = {
        ...data,
        // Convert empty date strings to null for PostgreSQL compatibility
        due_date: data.due_date && data.due_date.trim() !== "" ? data.due_date : null,
        paid_date: data.paid_date && data.paid_date.trim() !== "" ? data.paid_date : null,
        // Handle optional project_id (convert "none" to null)
        project_id: data.project_id && data.project_id.trim() !== "" && data.project_id !== "none" ? data.project_id : null,
        // Handle optional parent_invoice_id (convert empty string to null)
        parent_invoice_id: data.parent_invoice_id && data.parent_invoice_id.trim() !== "" ? data.parent_invoice_id : null,
        // Handle optional milestone_percentage (convert undefined/null to null)
        milestone_percentage: data.milestone_percentage !== undefined && data.milestone_percentage !== null ? data.milestone_percentage : null,
      }

      if (isEditing) {
        // For updates, exclude created_by field as it should not be modified
        const { created_by, ...updateData } = baseInvoiceData
        void created_by // Explicitly mark as intentionally unused
        await updateInvoice(invoice.id, updateData)
      } else {
        // For creation, include created_by field
        const currentUserId = await getCurrentUserIdOrFallback()
        const createData = {
          ...baseInvoiceData,
          created_by: currentUserId,
        }
        await createInvoice(createData)
      }

      onSuccess()
      onOpenChange(false)
      form.reset()
    } catch (error) {
      console.error("Failed to save invoice:", error)

      // Provide more specific error messages
      let errorMessage = "Failed to save invoice. Please try again."
      if (error instanceof Error) {
        if (error.message.includes("validation")) {
          errorMessage = "Please check all required fields and try again."
        } else if (error.message.includes("permission")) {
          errorMessage = "You don't have permission to perform this action."
        } else if (error.message.includes("network")) {
          errorMessage = "Network error. Please check your connection and try again."
        }
      }

      alert(errorMessage)
    } finally {
      // Always reset loading state to ensure button doesn't stay disabled
      setLoading(false)
    }
  }

  const addItem = () => {
    append({
      id: Date.now().toString(),
      description: "",
      quantity: 1,
      rate: 0,
      amount: 0,
    })
  }

  const removeItem = (index: number) => {
    if (fields.length > 1) {
      remove(index)
    }
  }

  return {
    form,
    fields,
    loading,
    isEditing,
    onSubmit,
    addItem,
    removeItem,
  }
}
