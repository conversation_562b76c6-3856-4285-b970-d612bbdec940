'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AnalyticsData } from "@/lib/api/analytics"
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from "recharts"

interface ServiceTypeChartProps {
  data: AnalyticsData
}

const COLORS = [
  '#3b82f6', // blue
  '#10b981', // green
  '#f59e0b', // yellow
  '#ef4444', // red
  '#8b5cf6', // purple
  '#06b6d4', // cyan
  '#84cc16', // lime
  '#f97316', // orange
  '#ec4899', // pink
  '#6b7280'  // gray
]

const formatServiceType = (serviceType: string) => {
  const serviceTypeMap: Record<string, string> = {
    'jasa_pembuatan_website': 'Pembuatan Website',
    'jasa_redesign_website': 'Redesign Website',
    'wordpress_hosting': 'WordPress Hosting',
    'jasa_maintenance_website': 'Maintenance Website',
    'jasa_perbaikan_website': 'Perbaikan Website',
    'jasa_remove_malware': 'Remove Malware',
    'jasa_migrasi_hosting': 'Migrasi Hosting',
    'jasa_migrasi_website_ke_astro': 'Migrasi ke Astro',
    'jasa_konversi_wordpress_ke_blocks': 'Konversi ke Blocks',
    'jasa_audit_optimasi_seo_onpage': 'Audit & SEO On-page'
  }
  
  return serviceTypeMap[serviceType] || serviceType
}

export function ServiceTypeChart({ data }: ServiceTypeChartProps) {
  const chartData = data.serviceTypes.map((item, index) => ({
    name: formatServiceType(item.service_type),
    value: item.count,
    percentage: item.percentage,
    color: COLORS[index % COLORS.length]
  }))

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-background border rounded-md shadow-md p-2 text-xs">
          <p className="font-medium">{data.name}</p>
          <p className="text-primary">Projects: {data.value}</p>
          <p className="text-muted-foreground">Percentage: {data.percentage.toFixed(1)}%</p>
        </div>
      )
    }
    return null
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Service Type Distribution</CardTitle>
        <CardDescription>
          Breakdown of projects by service type
        </CardDescription>
      </CardHeader>
      <CardContent>
        {chartData.length === 0 ? (
          <div className="h-[200px] flex items-center justify-center text-muted-foreground">
            No service type data to display
          </div>
        ) : (
          <div className="h-[200px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={2}
                  dataKey="value"
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend 
                  verticalAlign="bottom" 
                  height={36}
                  formatter={(value, entry) => (
                    <span style={{ color: entry.color, fontSize: '12px' }}>
                      {value} ({entry.payload?.value})
                    </span>
                  )}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
